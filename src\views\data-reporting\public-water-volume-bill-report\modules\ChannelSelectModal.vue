<template>
  <a-modal
    title="选择渠道"
    :visible="visible"
    :width="1700"
    :destroyOnClose="true"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <div class="channel-select-modal tree-table-page">
      <!-- 左侧基础类别 -->
      <div class="tree-table-tree-panel">
        <div class="tree-panel">
          <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
            <a-tab-pane key="1" tab="按行政区划" class="tab-pane">
              <TreeGeneral
                v-if="treeTabKey === '1'"
                :key="1"
                hasTab
                class="type-tree-panel"
                ref="treeGeneralRef"
                :treeOptions="districtTreeOptions"
                @onTreeMounted="onTreeMounted"
                @select="node => clickTreeNode(node)"
              />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>

      <!-- 右侧穿梭框区域 -->
      <div class="tree-table-right-panel">
        <div class="table-panel" layout="vertical">
          <!-- 数据授权 渠道类 -->
          <div class="transfer-panel" v-if="isShowTransfer" :title="tableTitle">
            <!-- 左侧 选择项 -->
            <div class="left-table">
              <div class="tab-title">
                <div class="left-span">选择项</div>
                <div class="right-span">共{{ leftDataTotal }}项</div>
              </div>

              <div class="tab-item">
                <div class="item-row">
                  <div class="item-col">
                    填报单位：
                    <a-select class="tab-input" v-model="leftParam.reportUnit" placeholder="请选择填报单位" allow-clear
                      @change="handleQuery">
                      <a-select-option
                        v-for="option in reportUnitOptions"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </a-select-option>
                    </a-select>
                  </div>
                  <div class="item-col">
                    渠道编码：
                    <a-input
                      class="tab-input"
                      v-model="leftParam.projectCode"
                      placeholder="请输入渠道编码"
                      allow-clear
                      @keyup.enter.native="handleQuery"
                    />
                  </div>
                  <div class="item-col">
                    渠道名称：
                    <a-input
                      class="tab-input"
                      v-model="leftParam.projectName"
                      placeholder="请输入渠道名称"
                      allow-clear
                      @keyup.enter.native="handleQuery"
                    />
                  </div>
                  <div class="item-col item-col-buttons">
                    <a-button type="primary" size="small" @click="handleQuery">查询</a-button>
                    <a-button size="small" @click="handleLeftReset">重置</a-button>
                  </div>
                </div>
              </div>

              <VxeTable
                ref="leftTableRef"
                class="vxetab-table"
                :otherHeight="modalOtherHeight"
                :isShowTableHeader="false"
                :columns="leftColumns"
                :tableData="leftData"
                :loading="leftLoading"
                :isAdaptPageSize="true"
                :height="modalTableHeight"
                @adaptPageSizeChange="adaptPageSizeChange"
                @selectChange="leftSelectChange"
                :tablePage="{ pageNum: leftParam.pageNum, pageSize: leftParam.pageSize, total: leftDataTotal }"
                @handlePageChange="handleLeftPageChange"
              />
            </div>

            <!-- 中间按钮 -->
            <div class="table-button">
              <div class="tab-group-div">
                <div class="tab-group">
                  <a-button size="small" class="tab-btn" type="primary" :disabled="!hasSelected" @click="addChannel">
                    <a-icon type="right" />
                  </a-button>
                  <a-button size="small" class="tab-btn" type="primary" :disabled="!hasRightSelected" @click="removeChannel">
                    <a-icon type="left" />
                  </a-button>
                </div>
              </div>
            </div>

            <!-- 右侧 已选项 -->
            <div class="right-table">
              <div class="tab-title">
                <div class="left-span">已选项</div>
                <div class="right-span">共{{ rightDataTotal }}项</div>
              </div>

              <div class="tab-item">
                <div class="item-row">
                  <div class="item-col">
                    填报单位：
                    <a-select class="tab-input" v-model="rightParam.reportUnit" placeholder="请选择填报单位" allow-clear
                      @change="handleRightQuery">
                      <a-select-option
                        v-for="option in reportUnitOptions"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </a-select-option>
                    </a-select>
                  </div>
                  <div class="item-col">
                    渠道编码：
                    <a-input
                      class="tab-input"
                      v-model="rightParam.projectCode"
                      placeholder="请输入渠道编码"
                      allow-clear
                      @keyup.enter.native="handleRightQuery"
                    />
                  </div>
                  <div class="item-col">
                    渠道名称：
                    <a-input
                      class="tab-input"
                      v-model="rightParam.projectName"
                      placeholder="请输入渠道名称"
                      allow-clear
                      @keyup.enter.native="handleRightQuery"
                    />
                  </div>
                  <div class="item-col item-col-buttons">
                    <a-button type="primary" size="small" @click="handleRightQuery">查询</a-button>
                    <a-button size="small" @click="handleRightReset">重置</a-button>
                  </div>
                </div>
              </div>

              <VxeTable
                ref="rightTableRef"
                class="vxetab-table"
                :otherHeight="modalOtherHeight"
                :isShowTableHeader="false"
                :columns="rightColumns"
                :tableData="rightData"
                :loading="rightLoading"
                :isAdaptPageSize="true"
                :height="modalTableHeight"
                @adaptPageSizeChange="adaptPageSizeChange"
                @selectChange="rightSelectChange"
                :tablePage="{ pageNum: rightParam.pageNum, pageSize: rightParam.pageSize, total: rightDataTotal }"
                @handlePageChange="handleRightPageChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import VxeTable from '@/components/VxeTable'
import TreeGeneral from '@/components/TreeGeneral'
import { getDistrictTree, getProjectPage } from '@/api/common'
import { getOrgTree } from '@/api/user'

export default {
  name: 'ChannelSelectModal',
  components: {
    VxeTable,
    TreeGeneral
  },
  computed: {
    // 获取当前用户的deptId
    currentUserDeptId() {
      const currentUserDepId = this.$store.state.user.loginOrgId || this.$store.state.user.deptId || 10020

      console.log('当前用户的部门id2', currentUserDepId)
      return currentUserDepId
    }
  },
  data() {
    return {
      visible: false,
      modalTableHeight: 490, // Modal内表格高度（增加90px）
      modalOtherHeight: 160, // Modal内其他元素高度（标题栏+查询条件+分页器+边距）

      // 行政区划树形组件配置
      districtTreeOptions: {
        dataSource: [],
        replaceFields: {
          children: 'children',
          title: 'districtName',
          key: 'districtId'
        }
      },

      // 当前选中的区划和类别
      selectedDistrict: null,
      selectedCategory: null,
      tableTitle: '渠道选择',

      // Tabs相关
      treeTabKey: '1',
      isShowTransfer: false,

      // 当前选中的ID
      selectedDistrictId: null,

      // 左侧参数
      leftParam: {
        pageNum: 1,
        pageSize: 10,
        reportUnit: null,
        projectCode: '',
        projectName: ''
      },

      // 右侧参数
      rightParam: {
        pageNum: 1,
        pageSize: 10,
        reportUnit: null,
        projectCode: '',
        projectName: ''
      },

      // 左侧数据
      leftData: [],
      leftDataTotal: 0,
      leftLoading: false,
      leftSelectedIds: [],
      leftSelectedRows: [],
      hasSelected: false,

      // 右侧数据
      rightData: [],
      rightDataTotal: 0,
      rightLoading: false,
      rightSelectedIds: [],
      rightSelectedRows: [],
      hasRightSelected: false,

      // 已选择的项目ID
      selectedProjectIds: [],

      // 缓存所有项目数据，避免重复API调用
      allProjectsCache: [],

      // 部门相关数据
      orgTree: [],
      reportUnitOptions: [],

      // 左侧表格列
      leftColumns: [
        { type: 'checkbox', width: 50 },
        {
          title: '渠道编号',
          field: 'projectCode',
          minWidth: 120
        },
        {
          title: '渠道名称',
          field: 'projectName',
          minWidth: 150
        }
      ],

      // 右侧表格列
      rightColumns: [
        { type: 'checkbox', width: 50 },
        {
          title: '渠道编号',
          field: 'projectCode',
          minWidth: 120
        },
        {
          title: '渠道名称',
          field: 'projectName',
          minWidth: 150
        }
      ]
    }
  },
  mounted() {
    // 组件挂载时加载行政区划数据
    this.loadDistrictTree()
    // 加载组织机构数据
    this.getOrgTreeData()
    // 计算Modal表格高度
    this.calculateModalTableHeight()
  },
  methods: {
    // 加载行政区划树数据
    async loadDistrictTree() {
      try {
        const response = await getDistrictTree()
        if (response.code === 200 && response.data) {
          this.districtTreeOptions.dataSource = response.data

          // 如果弹窗已经显示且没有选中区划，自动选中第一个
          if (this.visible && !this.selectedDistrict && response.data.length > 0) {
            await this.$nextTick()
            const firstNode = response.data[0]
            this.clickTreeNode(firstNode)
          }
        }
      } catch (error) {
        console.error('加载行政区划数据失败:', error)
        this.$message.error('加载行政区划数据失败')
      }
    },

    // 获取组织机构树数据
    async getOrgTreeData() {
      try {
        const response = await getOrgTree()
        if (response.success && response.data) {
          this.orgTree = response.data
          this.buildReportUnitOptions()
        }
      } catch (error) {
        console.error('获取组织机构树失败:', error)
      }
    },

    // 构建填报单位选项
    buildReportUnitOptions() {
      const options = []
      // 优先使用loginOrgId，其次使用deptId，最后使用默认值
      const currentUserDepId = this.$store.state.user.loginOrgId || this.$store.state.user.deptId || JSON.parse(localStorage.getItem('user')).orgId || 10020

      console.log('当前用户部门ID:', currentUserDepId)
      console.log('用户store信息:', this.$store.state.user)
      console.log('用户localstorage',JSON.parse(localStorage.getItem('user')).orgId)
      console.log('原始组织机构树:', this.orgTree)

      // 重构接口响应参数：将data[0].children[0]和data[0].children[0].children组成一个数组
      const restructuredData = this.restructureOrgData(this.orgTree)
      console.log('重构后的数据:', restructuredData)

      // 根据currentUserDepId过滤数据
      if (Number(currentUserDepId) === 10020) {
        // 如果是10020，显示全部
        restructuredData.forEach(dept => {
          options.push({
            value: dept.deptId,
            label: dept.deptName,
            deptId: dept.deptId
          })
        })
      } else {
        // 如果不是10020，仅显示匹配到的数据
        const matchedDepts = this.findMatchingDepts(restructuredData, currentUserDepId)
        matchedDepts.forEach(dept => {
          options.push({
            value: dept.deptId,
            label: dept.deptName,
            deptId: dept.deptId
          })
        })
      }

      this.reportUnitOptions = options
      console.log('构建的填报单位选项:', this.reportUnitOptions)

      // 设置默认值为第一项（当前用户部门）
      if (options.length > 0) {
        this.leftParam.reportUnit = options[0].value
        this.rightParam.reportUnit = options[0].value
      }
    },

    // 重构组织机构数据：将data[0].children[0]和data[0].children[0].children组成一个数组
    restructureOrgData(orgTree) {
      if (!orgTree || orgTree.length === 0) {
        return []
      }

      const result = []

      // 获取data[0].children[0]（永济分中心）
      if (orgTree[0] && orgTree[0].children && orgTree[0].children[0]) {
        const mainDept = orgTree[0].children[0] // 永济分中心
        // 去掉children属性后添加到结果数组
        const { children, ...mainDeptWithoutChildren } = mainDept
        result.push(mainDeptWithoutChildren)

        // 获取data[0].children[0].children（永济分中心的直接子部门）
        if (mainDept.children && mainDept.children.length > 0) {
          // 只添加直接子部门，去掉每一项的children属性
          mainDept.children.forEach(child => {
            const { children, ...childWithoutChildren } = child
            result.push(childWithoutChildren)
          })
        }
      }

      return result
    },

    // 查找匹配的部门（包括其直接子部门）
    findMatchingDepts(deptArray, targetDepId) {
      const result = []

      for (const dept of deptArray) {
        if (Number(dept.deptId) === Number(targetDepId)) {
          // 找到匹配的部门，添加该部门
          result.push(dept)

          // 如果该部门有直接子部门，也添加直接子部门（不递归）
          if (dept.children && dept.children.length > 0) {
            result.push(...dept.children)
          }
          break
        }
      }

      return result
    },

    // 显示弹窗
    show(selectedProjects = [], reportUnit = null) {
      this.visible = true
      this.selectedProjectIds = selectedProjects.map(item => item.projectId || item.id)

      // 设置填报单位：优先使用传入的reportUnit，其次使用默认值
      if (reportUnit) {
        // 如果传入了填报单位，使用传入的值
        this.leftParam.reportUnit = reportUnit
        this.rightParam.reportUnit = reportUnit
      } else if (this.reportUnitOptions.length > 0 && !this.leftParam.reportUnit) {
        // 如果没有传入填报单位且当前没有值，使用默认值
        this.leftParam.reportUnit = this.reportUnitOptions[0].value
        this.rightParam.reportUnit = this.reportUnitOptions[0].value
      }

      // 使用 nextTick 确保 DOM 更新完成后再执行自动选择
      this.$nextTick(() => {
        // 计算Modal内表格的实际可用高度
        this.calculateModalTableHeight()

        // 如果树数据已经加载，自动选中第一个节点
        if (this.districtTreeOptions.dataSource.length > 0) {
          const firstNode = this.districtTreeOptions.dataSource[0]
          this.clickTreeNode(firstNode)
        } else {
          // 如果树数据还没加载，只加载右侧已选数据
          this.loadRightData()
        }
      })
    },

    // 隐藏弹窗
    hide() {
      this.visible = false
      this.resetData()
    },

    // 重置数据
    resetData() {
      this.selectedDistrict = null
      this.selectedCategory = null
      this.selectedDistrictId = null
      this.isShowTransfer = false

      this.leftParam = {
        pageNum: 1,
        pageSize: 10,
        reportUnit: null,
        projectCode: '',
        projectName: ''
      }

      this.rightParam = {
        pageNum: 1,
        pageSize: 10,
        reportUnit: null,
        projectCode: '',
        projectName: ''
      }

      this.leftData = []
      this.leftDataTotal = 0
      this.leftSelectedIds = []
      this.leftSelectedRows = []
      this.hasSelected = false

      this.rightData = []
      this.rightDataTotal = 0
      this.rightSelectedIds = []
      this.rightSelectedRows = []
      this.hasRightSelected = false

      this.selectedProjectIds = []
    },

    // 树组件挂载完成
    onTreeMounted(treeData) {

      // 只有在弹窗显示且没有选中任何区划时才自动选中第一个节点
      if (this.visible && !this.selectedDistrict && treeData && treeData.length > 0) {
        const firstNode = treeData[0]

        // 使用延迟确保树组件完全初始化
        setTimeout(() => {
          this.clickTreeNode(firstNode)
        }, 100)
      }
    },

    // 点击树节点
    clickTreeNode(node) {
      const nodeData = node.dataRef || node

      // 确保有districtCode才进行查询
      if (!nodeData.districtCode) {
        console.warn('节点没有districtCode，无法查询项目数据')
        this.isShowTransfer = false
        return
      }
      this.selectedDistrict = nodeData.districtCode
      this.selectedCategory = nodeData.districtName
      this.selectedDistrictId = nodeData.districtId
      this.isShowTransfer = true
      this.$nextTick(() => {
        this.loadLeftData()
      })
    },

    // 加载左侧数据
    async loadLeftData() {
      if (!this.selectedDistrict) {
        this.leftData = []
        this.leftDataTotal = 0
        return
      }

      this.leftLoading = true

      try {
        // 如果缓存中没有当前区划的数据，才调用API
        const cacheKey = `${this.selectedDistrict}_${this.leftParam.reportUnit || this.currentUserDeptId}`
        if (!this.allProjectsCache.find(cache => cache.districtCode === cacheKey)) {
          const params = {
            pageNum: 1,
            pageSize: 99999, // 获取所有数据
            districtCode: this.selectedDistrict,
            orgId: this.leftParam.reportUnit || this.currentUserDeptId // 使用选择的填报单位或当前用户的deptId
          }
          
          // 检查是否有有效的orgId
          if (!this.currentUserDeptId) {
            console.warn('当前用户deptId为空，可能影响数据查询结果')
          }

          const response = await getProjectPage(params)
          if (response.code === 200 && response.data) {
            // 对项目数据进行过滤：过滤掉objectCategoryId !== 40 和 projectName中带有"闸"字的
            const filteredProjects = (response.data.data || []).filter(project => {
              // 过滤条件1：objectCategoryId 必须等于 40
              if (project.objectCategoryId !== 40) {
                return false
              }
              // 过滤条件2：projectName 不能包含"闸"字
              if (project.projectName && project.projectName.includes('闸')) {
                return false
              }
              return true
            })

            // 缓存当前区划的过滤后项目数据
            this.allProjectsCache.push({
              districtCode: cacheKey,
              projects: filteredProjects
            })
          }
        }

        // 从缓存中获取数据并过滤分页
        this.filterAndPaginateLeftData()

        // 同时加载右侧已选数据
        this.loadRightData()

      } catch (error) {
        console.error('加载项目数据失败:', error)
        this.$message.error('加载项目数据失败')
      } finally {
        this.leftLoading = false
      }
    },

    // 过滤和分页左侧数据
    filterAndPaginateLeftData() {
      const cacheKey = `${this.selectedDistrict}_${this.leftParam.reportUnit || this.currentUserDeptId}`
      const cache = this.allProjectsCache.find(cache => cache.districtCode === cacheKey)
      if (!cache) {
        this.leftData = []
        this.leftDataTotal = 0
        return
      }

      let filteredProjects = cache.projects.filter(project => {
        // 排除已选择的项目
        return !this.selectedProjectIds.includes(project.projectId)
      })

      // 按编码过滤
      if (this.leftParam.projectCode) {
        filteredProjects = filteredProjects.filter(item =>
          item.projectCode.toLowerCase().includes(this.leftParam.projectCode.toLowerCase())
        )
      }

      // 按名称过滤
      if (this.leftParam.projectName) {
        filteredProjects = filteredProjects.filter(item =>
          item.projectName.toLowerCase().includes(this.leftParam.projectName.toLowerCase())
        )
      }

      this.leftDataTotal = filteredProjects.length

      // 分页
      const start = (this.leftParam.pageNum - 1) * this.leftParam.pageSize
      const end = start + this.leftParam.pageSize
      this.leftData = filteredProjects.slice(start, end)

    },

    // 加载右侧已选数据
    loadRightData() {
      // 从缓存中获取所有已选择的项目
      let selectedProjects = []
      const addedProjectIds = new Set() // 用于去重

      if (this.selectedProjectIds.length > 0) {
        this.allProjectsCache.forEach(cache => {
          cache.projects.forEach(project => {
            if (this.selectedProjectIds.includes(project.projectId) && !addedProjectIds.has(project.projectId)) {
              selectedProjects.push(project)
              addedProjectIds.add(project.projectId) // 记录已添加的项目ID
            }
          })
        })
      } else {
        console.log('selectedProjectIds为空，没有选中任何项目')
      }

      // 按编码过滤
      if (this.rightParam.projectCode) {
        selectedProjects = selectedProjects.filter(item =>
          item.projectCode.toLowerCase().includes(this.rightParam.projectCode.toLowerCase())
        )
      }

      // 按名称过滤
      if (this.rightParam.projectName) {
        selectedProjects = selectedProjects.filter(item =>
          item.projectName.toLowerCase().includes(this.rightParam.projectName.toLowerCase())
        )
      }

      this.rightDataTotal = selectedProjects.length

      // 分页
      const start = (this.rightParam.pageNum - 1) * this.rightParam.pageSize
      const end = start + this.rightParam.pageSize
      this.rightData = selectedProjects.slice(start, end)

    },

    // 左侧查询
    handleQuery() {
      this.leftParam.pageNum = 1
      this.filterAndPaginateLeftData()
    },

    // 右侧查询
    handleRightQuery() {
      this.rightParam.pageNum = 1
      this.filterAndPaginateRightData()
    },

    // 过滤和分页右侧数据
    filterAndPaginateRightData() {
      this.loadRightData()
    },

    // 重置左侧查询
    handleLeftReset() {
      this.leftParam.projectCode = ''
      this.leftParam.projectName = ''
      this.leftParam.pageNum = 1
      // 重置填报单位为默认值
      if (this.reportUnitOptions.length > 0) {
        this.leftParam.reportUnit = this.reportUnitOptions[0].value
      }
      // 重新加载数据，因为填报单位改变了
      this.loadLeftData()
    },

    // 重置右侧查询
    handleRightReset() {
      this.rightParam.projectCode = ''
      this.rightParam.projectName = ''
      this.rightParam.pageNum = 1
      // 重置填报单位为默认值
      if (this.reportUnitOptions.length > 0) {
        this.rightParam.reportUnit = this.reportUnitOptions[0].value
      }
      this.filterAndPaginateRightData()
    },

    // 左侧分页变化
    handleLeftPageChange({ currentPage, pageSize }) {
      this.leftParam.pageNum = currentPage
      this.leftParam.pageSize = pageSize
      this.filterAndPaginateLeftData()
    },

    // 右侧分页变化
    handleRightPageChange({ currentPage, pageSize }) {
      this.rightParam.pageNum = currentPage
      this.rightParam.pageSize = pageSize
      this.filterAndPaginateRightData()
    },

    // 页面大小变化
    adaptPageSizeChange(pageSize) {
      this.leftParam.pageSize = pageSize
      this.rightParam.pageSize = pageSize
      this.filterAndPaginateLeftData()
      this.filterAndPaginateRightData()
    },

    // 左侧选择变化
    leftSelectChange(valObj) {
      this.leftSelectedIds = valObj.records.map(item => item.projectId)
      this.leftSelectedRows = valObj.records
      this.hasSelected = !!valObj.records.length
    },

    // 右侧选择变化
    rightSelectChange(valObj) {
      this.rightSelectedIds = valObj.records.map(item => item.projectId)
      this.rightSelectedRows = valObj.records
      this.hasRightSelected = !!valObj.records.length
    },

    // 添加渠道
    addChannel() {

      this.leftSelectedRows.forEach(row => {
        if (!this.selectedProjectIds.includes(row.projectId)) {
          this.selectedProjectIds.push(row.projectId)
        }
      })

      // 重新加载左右两侧数据
      this.filterAndPaginateLeftData()
      this.loadRightData()

      // 清空选择
      this.leftSelectedIds = []
      this.leftSelectedRows = []
      this.hasSelected = false

    },

    // 移除渠道
    removeChannel() {

      this.rightSelectedRows.forEach(row => {
        const index = this.selectedProjectIds.indexOf(row.projectId)
        if (index > -1) {
          this.selectedProjectIds.splice(index, 1)
        }
      })

      // 重新加载左右两侧数据
      this.filterAndPaginateLeftData()
      this.loadRightData()

      // 清空选择
      this.rightSelectedIds = []
      this.rightSelectedRows = []
      this.hasRightSelected = false

    },

    // 确定
    handleOk() {

      // 从缓存中获取所有已选择的项目详细信息
      let selectedProjects = []
      const addedProjectIds = new Set() // 用于去重

      if (this.selectedProjectIds.length > 0) {
        this.allProjectsCache.forEach(cache => {
          cache.projects.forEach(project => {
            if (this.selectedProjectIds.includes(project.projectId) && !addedProjectIds.has(project.projectId)) {
              selectedProjects.push(project)
              addedProjectIds.add(project.projectId) // 记录已添加的项目ID
            }
          })
        })
      } else {
        console.log('selectedProjectIds为空，没有选中任何项目')
      }

      // 格式化数据，确保包含渠道名、渠道编码和渠道id
      const formattedProjects = selectedProjects.map(project => ({
        id: project.projectId,
        channelCode: project.projectCode,
        channelName: project.projectName,
        projectId: project.projectId,
        projectCode: project.projectCode,
        projectName: project.projectName,
        objectCategoryName: project.objectCategoryName
      }))

      this.$emit('ok', formattedProjects)
      this.hide()
    },

    // 取消
    handleCancel() {
      this.hide()
    },
    
    // 计算Modal内表格的实际可用高度
    calculateModalTableHeight() {
      this.$nextTick(() => {
        // Modal总高度 600px
        const modalHeight = 600

        // 计算其他元素占用的高度（优化空间分配，给表格更多高度）
        const titleHeight = 30 // 标题栏高度（减少5px）
        const queryHeight = 50 // 查询条件区域高度（减少10px）
        const pagerHeight = 20 // 分页器高度（减少11px）
        const marginHeight = 0 // 各种边距和间距（减少14px）

        // 计算表格可用高度 - 现在约490px，比之前多了90px
        this.modalTableHeight = modalHeight - titleHeight - queryHeight - pagerHeight - marginHeight

        // 计算otherHeight，这个值会传给VxeTable组件用于自适应计算
        this.modalOtherHeight = titleHeight + queryHeight + pagerHeight + marginHeight
      })
    }
  }
}
</script>

<style lang="less" scoped>
.channel-select-modal {
    &.tree-table-page {
    display: flex;
    height: 600px;

    .tree-table-tree-panel {
      height: 100%;
      width: 260px;
      border-right: 1px solid #e8e8e8;
      background: #fafafa;

      .tree-panel {
        height: 100%;

        ::v-deep .ant-tabs {
          height: 100%;
          display: flex;
          flex-direction: column;

          .ant-tabs-bar {
            margin-bottom: 0;
            border-bottom: 1px solid #e8e8e8;
          }

          .ant-tabs-content {
            flex: 1;
            padding: 8px;
            overflow: hidden; // 改为hidden，不显示滚动条
          }

          .type-tree-panel {
            height: 100%;
            overflow: hidden; // 改为hidden，不显示滚动条

            .ant-tree {
              background: transparent;

              .ant-tree-node-content-wrapper {
                padding: 2px 4px;
                border-radius: 2px;

                &:hover {
                  background-color: #e6f7ff;
                }

                &.ant-tree-node-selected {
                  background-color: #bae7ff;
                }
              }
            }
          }
        }
      }
    }

    .tree-table-right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .table-panel {
        flex: 1;
        // padding: 16px;
        
        .transfer-panel {
          display: flex;
          align-items: stretch;
          height: 100%;
          
          .left-table, .right-table {
            flex: 1;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            
            .tab-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 16px;
              background: #fafafa;
              border-bottom: 1px solid #e8e8e8;
              
              .left-span {
                font-weight: 500;
                color: #333;
              }
              
              .right-span {
                color: #666;
                font-size: 12px;
              }
            }
            
            .tab-item {
              padding: 12px 16px;
              border-bottom: 1px solid #e8e8e8;
              
              .item-row {
                display: flex;
                gap: 12px;
                align-items: center;
                flex-wrap: wrap; // 允许换行

                .item-col {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  min-width: 200px; // 设置最小宽度

                  .tab-input {
                    flex: 1;
                    margin-left: 8px;
                    min-width: 120px; // 设置输入框最小宽度
                  }

                  &.item-col-buttons {
                    flex: none;
                    gap: 8px;
                    min-width: auto; // 按钮列不需要最小宽度

                    .ant-btn {
                      margin-left: 8px;
                    }
                  }
                }
              }
            }
            
            .vxetab-table {
              flex: 1;
              border: none;
              border-radius: 0;
              min-height: 490px; // 确保表格有足够的最小高度
            }
          }
          
          .table-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            margin: 0 10px;
            
            .tab-group-div {
              .tab-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
                
                .tab-btn {
                  width: 32px;
                  height: 32px;
                  padding: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 深度选择器样式 - 确保Modal内表格正确显示
::v-deep .vxetab-table {
  .vxe-table-content {
    height: 100% !important;
  }

  .vxe-table-box {
    height: calc(100% - 15px) !important;
  }

  .vxe-table-box-content {
    height: 100% !important;
    overflow: hidden !important;
  }

  .vxe-grid {
    height: 100% !important;
  }

  // 确保表格体不出现滚动条
  .vxe-table--body-wrapper {
    overflow: hidden !important;
  }
  
  .vxe-table--header {
    background-color: #fafafa;
  }
}

// 全局树结构滚动样式
::v-deep .tab-tree-panel-box {
  overflow-y: auto;
  overflow-x: hidden;

  .ant-tree {
    background: transparent;

    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 2px;

      &:hover {
        background-color: #e6f7ff;
      }

      &.ant-tree-node-selected {
        background-color: #bae7ff;
      }
    }
  }
}
</style>
