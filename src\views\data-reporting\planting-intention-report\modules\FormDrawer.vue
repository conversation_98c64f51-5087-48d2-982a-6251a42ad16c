<template>
    <!-- 增加修改 -->
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="1800"
      @cancel="cancel"
      modalHeight="800"
      @ok="handleSave"
    >
    <div slot="content">
      <div class="planting-intention-report">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="form-row">
            <div class="form-item">
              <label>计划年份：</label>
              <a-date-picker
                v-model="formData.planYear"
                mode="year"
                format="YYYY"
                placeholder="请选择年份"
                style="width: 200px"
                :disabled="isEdit"
                :open="yearShowOne"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              />
            </div>
            <div class="form-item">
              <label>填报单位：</label>
              <a-select
                v-model="formData.reportUnit"
                placeholder="请选择填报单位"
                style="width: 200px"
                :disabled="isEdit"
              >
                <a-select-option 
                  v-for="option in reportUnitOptions" 
                  :key="option.value" 
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
        </div>

        <!-- 引黄灌溉（按渠道）部分 -->
        <IrrigationPlantingTable
          ref="yellowRiverIrrigationTableRef"
          title="引黄灌溉（按渠道）"
          :table-data="yellowRiverIrrigationData"
          :report-unit="formData.reportUnit"
          table-ref="yellowRiverIrrigationTableRef"
          @row-change="handleYellowRiverIrrigationChange"
          @update-table-data="handleYellowRiverIrrigationTableUpdate"
        />

        <!-- 引黄滴灌（按渠道）部分 -->
        <IrrigationPlantingTable
          ref="yellowRiverDripIrrigationTableRef"
          title="引黄滴灌（按渠道）"
          :table-data="yellowRiverDripIrrigationData"
          :report-unit="formData.reportUnit"
          table-ref="yellowRiverDripIrrigationTableRef"
          @row-change="handleYellowRiverDripIrrigationChange"
          @update-table-data="handleYellowRiverDripIrrigationTableUpdate"
        />

        <!-- 纯井灌（按渠道）部分 -->
        <IrrigationPlantingTable
          ref="wellIrrigationTableRef"
          title="纯井灌（按渠道）"
          :table-data="wellIrrigationData"
          :report-unit="formData.reportUnit"
          table-ref="wellIrrigationTableRef"
          @row-change="handleWellIrrigationChange"
          @update-table-data="handleWellIrrigationTableUpdate"
        />

      </div>
    </div>
    </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
import IrrigationPlantingTable from './IrrigationPlantingTable'
import moment from 'moment'
import { addIrrigationPlantIntent, getIrrigationPlantIntentById, updateIrrigationPlantIntent } from '../service'
import { getLeafCropsWithFieldNames, findCropByFieldName, getValueToFieldNameMap, findCropByPlantTypeCode, findCropByTypeCodes } from '@/enum/planting-crop'
import { getOptions } from '@/api/common'
import request from '@/utils/request'
import user from '@/store/modules/user'
import { getOrgTree } from '@/api/user'

  export default {
    name: 'FormDrawer',
    props: [],
    components: { AntModal, IrrigationPlantingTable },
    data() {
      return {
        open: false,
        modalLoading: false,
        formTitle: '',
        isEdit: false,
        editId: null,
        yearShowOne: false, // 添加年份选择器显示状态
        formData: {
          planYear: moment(), // 默认为当前年份
          reportUnit: null
        },
        // 部门相关数据
        orgTree: [],
        reportUnitOptions: [],
        // 作物字典选项
        cropOptions: [],
        // 项目（渠道）数据
        projectOptions: [],
        projectOptionsLoading: false,
        projectOptionsLoaded: false,
        yellowRiverIrrigationData: [],
        yellowRiverDripIrrigationData: [],
        wellIrrigationData: []
      }
    },
    created() {
      this.loadCropOptions()
      this.getOrgTreeData()
    },
    mounted() {
      // 如果用户信息已经加载但部门选项还没有构建，重新构建
      if (this.orgTree.length > 0 && this.reportUnitOptions.length === 0) {
        this.buildReportUnitOptions()
      }
    },
    methods: {
      cancel() {
        this.open = false
        this.resetData()
      },
      
      handleAdd() {
        this.open = true
        this.formTitle = '新增'
        this.isEdit = false
        this.editId = null
        
        // 确保组织机构数据已加载后再初始化
        if (this.reportUnitOptions.length > 0) {
          this.initData()
        } else {
          // 如果组织机构数据还没加载，等待加载完成后再初始化
          const checkAndInit = () => {
            if (this.reportUnitOptions.length > 0) {
              this.initData()
            } else {
              setTimeout(checkAndInit, 100)
            }
          }
          checkAndInit()
        }
      },
      
      async handleUpdate(record) {
        this.open = true
        this.formTitle = '编辑'
        this.isEdit = true
        this.editId = record.id
        this.modalLoading = true
        
        try {
          await this.loadEditData(record.id)
        } catch (error) {
          console.error('加载编辑数据失败:', error)
          this.$message.error('加载编辑数据失败')
        } finally {
          this.modalLoading = false
        }
      },
      
      // 重置数据
      resetData() {
        this.formData = {
          planYear: moment(), // 默认为当前年份
          reportUnit: null
        }
        this.yellowRiverIrrigationData = []
        this.yellowRiverDripIrrigationData = []
        this.wellIrrigationData = []
        this.isEdit = false
        this.editId = null
      },
      
      // 初始化数据（新增时使用）
      initData() {
        this.formData.planYear = moment() // 默认为当前年份
        
        // 如果填报单位选项已加载，默认选择第一项
        if (this.reportUnitOptions.length > 0) {
          this.formData.reportUnit = this.reportUnitOptions[0].value
        } else {
          this.formData.reportUnit = null
        }
        
        this.yellowRiverIrrigationData = []
        this.yellowRiverDripIrrigationData = []
        this.wellIrrigationData = []
      },

      // 加载编辑数据
      async loadEditData(id) {
        try {
          
          // 确保项目数据已加载
          if (!this.projectOptionsLoaded && !this.projectOptionsLoading) {
            await this.loadProjectOptions()
          } else if (this.projectOptionsLoading) {
            // 等待加载完成
            while (this.projectOptionsLoading) {
              await new Promise(resolve => setTimeout(resolve, 100))
            }
          }
          
          const response = await getIrrigationPlantIntentById(id)
          
          if (response.code === 200 && response.data) {
            const data = response.data
            
            // 设置基本信息
            this.formData.planYear = moment(data.planYear, 'YYYY')
            this.formData.reportUnit = data.depId
            
            // 解析灌溉数据
            this.parseIrrigationData(data.cstIrrigationDtoList || [])

          } else {
            console.error('获取编辑数据失败:', response)
            this.$message.error(response.message || '获取编辑数据失败')
          }
        } catch (error) {
          console.error('获取编辑数据失败:', error)
          throw error
        }
      },

      // 解析灌溉数据
      parseIrrigationData(cstIrrigationDtoList) {
        // 重置数据
        this.yellowRiverIrrigationData = []
        this.yellowRiverDripIrrigationData = []
        this.wellIrrigationData = []
        
        cstIrrigationDtoList.forEach(irrigationDto => {
          const { irrigationType, channelDtoList } = irrigationDto
          
          // 根据灌溉类型分类处理
          const tableData = this.parseChannelData(channelDtoList || [])
          
          switch (irrigationType) {
            case 1: // 引黄灌溉
              this.yellowRiverIrrigationData = tableData
              break
            case 2: // 引黄滴灌
              this.yellowRiverDripIrrigationData = tableData
              break
            case 3: // 纯井灌
              this.wellIrrigationData = tableData
              break
            default:
              console.warn('未知的灌溉类型:', irrigationType)
          }
        })
      },

      // 解析渠道数据
      parseChannelData(channelDtoList) {
        const tableData = []
        
        channelDtoList.forEach((channelDto, index) => {
          const { channelNo, cstIrrigationPlantIntentDetailDtoList } = channelDto
          
          // 获取渠道名称
          const channelName = this.getChannelName(channelNo)
          
          // 根据渠道编码查找项目ID
          const project = this.projectOptions.find(option => option.projectCode === channelNo)
          const projectId = project ? project.projectId : `${channelNo}_${index}`
          
          // 创建行数据
          const rowData = {
            id: projectId,
            projectId: projectId,
            channelCode: channelNo,
            channel: channelName,
            farmlandArea: null,
            totalIrrigationArea: null
          }
          
          // 初始化所有作物字段
          this.initializeCropFields(rowData)
          
          // 填充种植数据
          this.fillPlantingData(rowData, cstIrrigationPlantIntentDetailDtoList || [])
          
          // 计算小计和总计
          this.calculateSubtotals(rowData)
          
          tableData.push(rowData)
        })
        
        return tableData
      },

      // 初始化作物字段
      initializeCropFields(rowData) {
        const leafCrops = getLeafCropsWithFieldNames()
        leafCrops.forEach(crop => {
          if (crop.fieldName) {
            rowData[crop.fieldName] = null
          }
        })
        
        // 初始化小计字段
        const subtotalFields = [
          'melonVegetableSubtotal', 'wheatIntercroppingSubtotal', 'summerCropsSubtotal',
          'autumnCropsSubtotal', 'forestlandSubtotal', 'forestGrasslandSubtotal'
        ]
        subtotalFields.forEach(field => {
          rowData[field] = null
        })
      },

      // 填充种植数据
      fillPlantingData(rowData, plantIntentDetailList) {
        const valueToFieldNameMap = getValueToFieldNameMap()
        
        plantIntentDetailList.forEach(detail => {
          const { plantTypeCode, plantAmount, firstTypeCode, secondTypeCode } = detail
          
          // 根据plantTypeCode查找对应的作物
          const cropInfo = this.findCropByTypeCode(plantTypeCode, firstTypeCode, secondTypeCode)
          
          if (cropInfo && cropInfo.fieldName) {
            rowData[cropInfo.fieldName] = plantAmount
          } else {
            console.warn('未找到对应的作物字段:', { plantTypeCode, firstTypeCode, secondTypeCode })
          }
        })
      },

      // 根据类型编码查找作物
      findCropByTypeCode(plantTypeCode, firstTypeCode, secondTypeCode) {
        
        // 使用新的精确匹配方法
        const cropInfo = findCropByTypeCodes(plantTypeCode, firstTypeCode, secondTypeCode, this.cropOptions)
        
        if (cropInfo) {
          return cropInfo
        }
        
        console.warn('未找到匹配的作物:', { plantTypeCode, firstTypeCode, secondTypeCode })
        return null
      },

      // 计算小计和总计
      calculateSubtotals(row) {
        // 计算瓜菜小计
        const melon = parseFloat(row.melon) || 0
        const sunflowerInMelon = parseFloat(row.sunflowerInMelon) || 0
        const tomato = parseFloat(row.tomato) || 0
        row.melonVegetableSubtotal = (melon + sunflowerInMelon + tomato) || null

        // 计算小麦间套种小计
        const corn = parseFloat(row.corn) || 0
        const pepper = parseFloat(row.pepper) || 0
        const sunflowerInWheat = parseFloat(row.sunflowerInWheat) || 0
        const otherInWheat = parseFloat(row.otherInWheat) || 0
        row.wheatIntercroppingSubtotal = (corn + pepper + sunflowerInWheat + otherInWheat) || null

        // 计算夏季作物小计
        const wheat = parseFloat(row.wheat) || 0
        const oilCrop = parseFloat(row.oilCrop) || 0
        const summerMisc = parseFloat(row.summerMisc) || 0
        const melonVegetableSubtotal = parseFloat(row.melonVegetableSubtotal) || 0
        const wheatIntercroppingSubtotal = parseFloat(row.wheatIntercroppingSubtotal) || 0
        row.summerCropsSubtotal = (wheat + oilCrop + summerMisc + melonVegetableSubtotal + wheatIntercroppingSubtotal) || null

        // 计算秋季作物小计
        const autumnCorn = parseFloat(row.autumnCorn) || 0
        const gourd = parseFloat(row.gourd) || 0
        const autumnPepper = parseFloat(row.autumnPepper) || 0
        const dehydratedVegetables = parseFloat(row.dehydratedVegetables) || 0
        const autumnSunflower = parseFloat(row.autumnSunflower) || 0
        const autumnMisc = parseFloat(row.autumnMisc) || 0
        row.autumnCropsSubtotal = (autumnCorn + gourd + autumnPepper + dehydratedVegetables + autumnSunflower + autumnMisc) || null

        // 计算林地小计
        const matureForest = parseFloat(row.matureForest) || 0
        const youngForest = parseFloat(row.youngForest) || 0
        const fruitTree = parseFloat(row.fruitTree) || 0
        const wolfberry = parseFloat(row.wolfberry) || 0
        row.forestlandSubtotal = (matureForest + youngForest + fruitTree + wolfberry) || null

        // 计算林牧地小计
        const forestlandSubtotal = parseFloat(row.forestlandSubtotal) || 0
        const grassland = parseFloat(row.grassland) || 0
        row.forestGrasslandSubtotal = (forestlandSubtotal + grassland) || null

        // 计算耕地面积（夏季作物小计 + 秋季作物小计）
        const summerCropsSubtotal = parseFloat(row.summerCropsSubtotal) || 0
        const autumnCropsSubtotal = parseFloat(row.autumnCropsSubtotal) || 0
        row.farmlandArea = (summerCropsSubtotal + autumnCropsSubtotal) || null

        // 计算总灌溉面积（耕地面积 + 林牧地小计）
        const farmlandArea = parseFloat(row.farmlandArea) || 0
        const forestGrasslandSubtotal = parseFloat(row.forestGrasslandSubtotal) || 0
        row.totalIrrigationArea = (farmlandArea + forestGrasslandSubtotal) || null
      },

      // 获取渠道名称
      getChannelName(channelNo) {
        if (this.projectOptions.length === 0) {
          console.warn('项目数据未加载，返回原始渠道编码')
          return channelNo
        }
        
        const project = this.projectOptions.find(option => option.projectCode === channelNo)
        return project ? project.projectName : channelNo
      },

      // 获取组织机构树数据
      async getOrgTreeData() {
        try {
          const response = await getOrgTree()
          if (response.success && response.data) {
            this.orgTree = response.data
            this.buildReportUnitOptions()
          }
        } catch (error) {
          console.error('获取组织机构树失败:', error)
        }
      },
      
      // 构建填报单位选项
      buildReportUnitOptions() {
        const options = []
        // 优先使用loginOrgId，其次使用deptId，最后使用默认值
        const currentUserDepId = this.$store.state.user.loginOrgId || this.$store.state.user.deptId || 10020
        
        // 递归查找匹配的部门
        const findMatchingDept = (nodes, targetDepId) => {
          for (const node of nodes) {
            // 确保ID比较时类型一致
            if (Number(node.deptId) === Number(targetDepId)) {
              return node
            }
            if (node.children && node.children.length > 0) {
              const found = findMatchingDept(node.children, targetDepId)
              if (found) return found
            }
          }
          return null
        }
        
        const matchedDept = findMatchingDept(this.orgTree, currentUserDepId)
        
        if (matchedDept) {
          // 添加当前部门
          options.push({
            value: matchedDept.deptId,
            label: matchedDept.deptName,
            deptId: matchedDept.deptId
          })
          
          // 如果有子部门，也添加子部门
          if (matchedDept.children && matchedDept.children.length > 0) {
            matchedDept.children.forEach(child => {
              options.push({
                value: child.deptId,
                label: child.deptName,
                deptId: child.deptId
              })
            })
          }
        }
        
        this.reportUnitOptions = options
        
        // 如果是新增模式且没有设置填报单位，默认选择第一项
        if (!this.isEdit && !this.formData.reportUnit && options.length > 0) {
          this.formData.reportUnit = options[0].value
        }
      },

      // 加载作物选项字典
      async loadCropOptions() {
        try {
          const response = await getOptions('crop')
          if (response.code === 200 && response.data) {
            this.cropOptions = response.data.map(item => ({
              key: item.key,
              value: item.value,
              label: item.value
            }))
          }
        } catch (error) {
          console.error('获取作物选项失败:', error)
        }
      },

      // 加载项目（渠道）选项
      async loadProjectOptions() {
        try {
          console.log('开始加载项目数据...')
          this.projectOptionsLoading = true
          
          const response = await request({
            url: '/base/project/page',
            method: 'post',
            data: {
              pageNum: 1,
              pageSize: 1000,
              districtCode: "0"
            }
          })
          
          if (response.code === 200 && response.data && response.data.data) {
            this.projectOptions = response.data.data.map(item => ({
              projectId: item.projectId,
              projectCode: item.projectCode,
              projectName: item.projectName,
              projectNameAbbr: item.projectNameAbbr,
              objectCategoryName: item.objectCategoryName
            }))
            
            console.log('项目数据加载完成:', this.projectOptions.length, '条')
            this.projectOptionsLoaded = true
          } else {
            console.error('获取项目数据失败:', response.message)
            this.$message.error('获取项目数据失败: ' + (response.message || '未知错误'))
          }
        } catch (error) {
          console.error('获取项目数据失败:', error)
          this.$message.error('获取项目数据失败: ' + (error.message || '网络错误'))
        } finally {
          this.projectOptionsLoading = false
        }
      },

      // 处理引黄灌溉数据变化
      handleYellowRiverIrrigationChange(row) {
        // 不需要计算合计行，因为现在使用 footerData 自动计算
      },

      // 处理引黄滴灌数据变化
      handleYellowRiverDripIrrigationChange(row) {
        // 不需要计算合计行，因为现在使用 footerData 自动计算
      },

      // 处理纯井灌数据变化
      handleWellIrrigationChange(row) {
        // 不需要计算合计行，因为现在使用 footerData 自动计算
      },

      // 处理引黄灌溉表格数据更新
      handleYellowRiverIrrigationTableUpdate(newTableData) {
        this.yellowRiverIrrigationData = newTableData
      },

      // 处理引黄滴灌表格数据更新
      handleYellowRiverDripIrrigationTableUpdate(newTableData) {
        this.yellowRiverDripIrrigationData = newTableData
      },

      // 处理纯井灌表格数据更新
      handleWellIrrigationTableUpdate(newTableData) {
        this.wellIrrigationData = newTableData
      },

      // 保存
      async handleSave() {
        try {
          // 验证基本信息
          if (!this.formData.planYear) {
            this.$message.error('请选择计划年份')
            return
          }
          if (!this.formData.reportUnit) {
            this.$message.error('请选择填报单位')
            return
          }

          this.modalLoading = true

          // 构建请求数据
          const requestData = this.buildRequestData()
          
          // 调用接口
          let response
          if (this.isEdit) {
            response = await updateIrrigationPlantIntent(requestData)
          } else {
            response = await addIrrigationPlantIntent(requestData)
          }
          
          if (response.code === 200) {
            this.$message.success(this.isEdit ? '更新成功' : '保存成功')
            this.open = false
            this.$emit('ok')
          } else {
            this.$message.error(response.message || (this.isEdit ? '更新失败' : '保存失败'))
          }
        } catch (error) {
          console.error('保存失败:', error)
          // this.$message.error('保存失败：' + (error || '网络错误'))
        } finally {
          this.modalLoading = false
        }
      },

      // 构建请求数据
      buildRequestData() {
        const requestData = {
          cstIrrigationDtoList: [],
          depId: this.formData.reportUnit || user.state.deptId,
          planYear: this.formData.planYear.year()
        }
        
        // 如果是编辑模式，添加ID
        if (this.isEdit && this.editId) {
          requestData.id = this.editId
        }

        // 处理引黄灌溉数据 (灌溉类型: 1)
        if (this.yellowRiverIrrigationData.length > 0) {
          const channelDtoList = this.buildChannelDtoList(this.yellowRiverIrrigationData)
          if (channelDtoList.length > 0) {
            requestData.cstIrrigationDtoList.push({
              irrigationType: 1,
              channelDtoList: channelDtoList
            })
          }
        }

        // 处理引黄滴灌数据 (灌溉类型: 2)
        if (this.yellowRiverDripIrrigationData.length > 0) {
          const channelDtoList = this.buildChannelDtoList(this.yellowRiverDripIrrigationData)
          if (channelDtoList.length > 0) {
            requestData.cstIrrigationDtoList.push({
              irrigationType: 2,
              channelDtoList: channelDtoList
            })
          }
        }

        // 处理纯井灌数据 (灌溉类型: 3)
        if (this.wellIrrigationData.length > 0) {
          const channelDtoList = this.buildChannelDtoList(this.wellIrrigationData)
          if (channelDtoList.length > 0) {
            requestData.cstIrrigationDtoList.push({
              irrigationType: 3,
              channelDtoList: channelDtoList
            })
          }
        }

        return requestData
      },

      // 构建渠道数据列表
      buildChannelDtoList(tableData) {
        const channelDtoList = []

        tableData.forEach(row => {
          if (!row.channelCode) return // 跳过没有渠道编码的行

          const cstIrrigationPlantIntentDetailDtoList = []
          
          // 获取所有叶子节点作物
          const leafCrops = getLeafCropsWithFieldNames()
          
          leafCrops.forEach(crop => {
            if (crop.fieldName && row[crop.fieldName] != null && row[crop.fieldName] !== '') {
              const plantAmount = parseFloat(row[crop.fieldName])
              if (plantAmount > 0) {
                // 获取作物的字典key
                const cropKey = this.getCropKeyFromTable(crop.fieldName)
                
                cstIrrigationPlantIntentDetailDtoList.push({
                  firstTypeCode: parseInt(crop.firstTypeCode) || 0,
                  secondTypeCode: parseInt(crop.secondTypeCode) || 0,
                  plantAmount: plantAmount,
                  plantTypeCode: cropKey || 0
                })
              }
            }
          })

          // 如果有种植数据，添加到渠道列表
          if (cstIrrigationPlantIntentDetailDtoList.length > 0) {
            channelDtoList.push({
              channelNo: row.channelCode,
              cstIrrigationPlantIntentDetailDtoList: cstIrrigationPlantIntentDetailDtoList
            })
          }
        })

        return channelDtoList
      },

      // 从表格组件获取作物字典key
      getCropKeyFromTable(fieldName) {
        // 尝试从三个表格组件中获取作物key
        const tables = [
          this.$refs.yellowRiverIrrigationTableRef,
          this.$refs.yellowRiverDripIrrigationTableRef,
          this.$refs.wellIrrigationTableRef
        ]

        for (let tableRef of tables) {
          if (tableRef && typeof tableRef.getCropKey === 'function') {
            const cropKey = tableRef.getCropKey(fieldName)
            if (cropKey) {
              return cropKey
            }
          }
        }

        return null
      },

      // 年份选择器打开/关闭事件
      openChangeOne(status) {
        if (status) {
          this.yearShowOne = true
        }
      },

      // 年份选择器面板变化事件
      panelChangeOne(value) {
        this.formData.planYear = moment(value)
        this.yearShowOne = false
      }
    }
  }

</script>

<style lang="less" scoped>
.planting-intention-report {
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .form-row {
      display: flex;
      gap: 30px;
      
      .form-item {
        display: flex;
        align-items: center;
        
        label {
          margin-right: 8px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .irrigation-section {
    margin-bottom: 30px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .irrigation-table {
      width: 100%;
      
      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }
      
      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
        font-size: 12px;
      }
      
      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
        
        &:last-child {
          background-color: #f0f8ff;
          font-weight: bold;
        }
      }
      
      /deep/ .vxe-table {
        width: 100% !important;
        font-size: 12px;
      }
      
      /deep/ .vxe-cell {
        padding: 4px 8px;
      }
    }
    
    .table-placeholder {
      background: #f9f9f9;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      padding: 40px;
      text-align: center;
      color: #999;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e8e8e8;
  }

  .action-buttons {
    text-align: center;
    margin-top: 20px;
    
    .ant-btn {
      margin: 0 10px;
      min-width: 80px;
    }
  }
}
</style>