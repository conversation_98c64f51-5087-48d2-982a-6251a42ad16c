<template>
  <!-- 增加修改 -->
  <ant-modal :visible="open" :modal-title="formTitle" :loading="modalLoading" modalWidth="1500" @cancel="cancel"
    modalHeight="800">
    <div slot="content">
      <div class="irrigation-rounds-crop-reporting">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="form-row">
            <div class="form-item">
              <label>日期：</label>
              <a-date-picker v-if="!isEdit" v-model="formData.date" placeholder="请选择日期" style="width: 200px"
                @change="onDateChange" :disabledDate="disabledDate" />
              <span v-else class="date-display">
                {{ formatDateDisplay(formData.date) }}
              </span>
            </div>
            <div class="form-item">
              <label>轮次：</label>
              <span class="round-display">
                {{ getRoundLabel(formData.round) || (isEdit ? '' : '请先选择日期') }}
              </span>
            </div>
          </div>
        </div>

        <!-- 水情表部分 -->
        <div class="water-table-section">
          <h3 class="section-title">水情表</h3>
          <vxe-table ref="waterTableRef" :data="tableData" border stripe :row-config="{ keyField: 'id' }"
            :column-config="{ resizable: true }" :edit-config="{ trigger: 'manual', mode: 'cell', showStatus: true }"
            :span-method="mergeRowMethod" height="400" class="water-table" width="100%" header-align="center"
            align="center">
            <!-- 灌域列 -->
            <vxe-column field="irrigationArea" title="灌域" width="100">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.irrigationArea }}
                </span>
              </template>
            </vxe-column>

            <!-- 渠道列 -->
            <vxe-column field="channelGroup" title="渠道" width="110">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.channelGroup }}
                </span>
              </template>
            </vxe-column>
            <vxe-column field="channel" title="" width="110">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.channel }}
                </span>
              </template>
            </vxe-column>

            <!-- 流量列组 -->
            <vxe-colgroup title="流量">
              <vxe-column field="flow8" title="8时" width="120">
                <template #default="{ row }">
                  <a-input-number v-if="!row.isTotal" v-model="row.flow8" :precision="2" :min="0" style="width: 100%"
                    @change="onFlowChange(row)" />
                  <span v-else>{{ row.flow8 || '-' }}</span>
                </template>
              </vxe-column>
              <vxe-column field="flow18" title="18时" width="120">
                <template #default="{ row }">
                  <a-input-number v-if="!row.isTotal" v-model="row.flow18" :precision="2" :min="0" style="width: 100%"
                    @change="onFlowChange(row)" />
                  <span v-else>{{ row.flow18 || '-' }}</span>
                </template>
              </vxe-column>
            </vxe-colgroup>

            <!-- 日均流量列 -->
            <vxe-column field="dailyFlow" title="日均流量（m³/s）" width="140">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.dailyFlow != null ? row.dailyFlow : '-' }}
                </span>
              </template>
            </vxe-column>

            <!-- 累计流量日列 -->
            <vxe-column field="cumulativeFlow" title="累计流量日" width="120">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.cumulativeFlow != null ? row.cumulativeFlow : '-' }}
                </span>
              </template>
            </vxe-column>

            <!-- 备注列 -->
            <vxe-column field="remark" title="备注" width="180">
              <template #default="{ row }">
                <a-input v-if="!row.isTotal" v-model="row.remark" style="width: 100%" />
                <span v-else>{{ row.remark || '-' }}</span>
              </template>
            </vxe-column>

            <!-- 放关口列组 -->
            <vxe-colgroup title="放关口">
              <vxe-column field="openTime" title="放口" width="180">
                <template #default="{ row, rowIndex }">
                  <a-time-picker v-if="!row.isTotal" v-model="row.openTime" format="HH:mm" style="width: 100%"
                    @change="(time) => onTimeChange(row, 'openTime', time)" allowClear placeholder="点击选择时间" />
                  <span v-else>-</span>
                </template>
              </vxe-column>
              <vxe-column field="closeTime" title="关口" width="180">
                <template #default="{ row, rowIndex }">
                  <a-time-picker v-if="!row.isTotal" v-model="row.closeTime" format="HH:mm" style="width: 100%"
                    @change="(time) => onTimeChange(row, 'closeTime', time)" allowClear placeholder="点击选择时间" />
                  <span v-else>-</span>
                </template>
              </vxe-column>
            </vxe-colgroup>
          </vxe-table>
        </div>

        <!-- 永济干渠水位流量表部分 -->
        <div class="canal-table-section">
          <!-- <h3 class="section-title">永济干渠水位流量表</h3> -->
          <vxe-table ref="canalTableRef" :data="canalTableData" border stripe :row-config="{ keyField: 'id' }"
            :column-config="{ resizable: true }" :edit-config="{ trigger: 'manual', mode: 'cell', showStatus: true }"
            :span-method="mergeCanalRowMethod" height="300" class="canal-table" width="100%" header-align="center"
            align="center">
            <!-- 永济干渠列 -->
            <vxe-column field="canalName" title="永济干渠" width="150">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.canalName }}
                </span>
              </template>
            </vxe-column>

            <!-- 永济节制闸列 -->
            <vxe-column field="gateName" title="永济节制闸" width="120">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.gateName }}
                </span>
              </template>
            </vxe-column>

            <!-- 闸位列 -->
            <vxe-column field="gatePosition" title="" width="100">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.gatePosition }}
                </span>
              </template>
            </vxe-column>

            <!-- 水位列组 -->
            <vxe-colgroup title="水位（m）">
              <vxe-column field="waterLevel8" title="8时" width="120">
                <template #default="{ row }">
                  <a-input-number v-model="row.waterLevel8" :precision="2" :min="0" style="width: 100%" />
                </template>
              </vxe-column>
              <vxe-column field="waterLevel18" title="18时" width="120">
                <template #default="{ row }">
                  <a-input-number v-model="row.waterLevel18" :precision="2" :min="0" style="width: 100%" />
                </template>
              </vxe-column>
            </vxe-colgroup>

            <!-- 流量列组 -->
            <vxe-colgroup title="流量（m³/s）">
              <vxe-column field="canalFlow8" title="8时" width="120">
                <template #default="{ row }">
                  <a-input-number v-model="row.canalFlow8" :precision="2" :min="0" style="width: 100%" />
                </template>
              </vxe-column>
              <vxe-column field="canalFlow18" title="18时" width="120">
                <template #default="{ row }">
                  <a-input-number v-model="row.canalFlow18" :precision="2" :min="0" style="width: 100%" />
                </template>
              </vxe-column>
            </vxe-colgroup>
          </vxe-table>
        </div>
      </div>
    </div>

    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="handleSave" :loading="loading">
        {{ isEdit ? '修改' : '保存' }}
      </a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'
import moment from 'moment'
import { getIrrigationRound, getProjectChildren, addWaterSituationReport, updateIrrigationWaterSituation, getIrrigationWaterSituationById, calculateIrrigationWaterSituation } from '../service'

export default {
  name: 'FormDrawer',
  props: [],
  components: { AntModal },
  data() {
    return {
      open: false,
      modalLoading: false,
      loading: false,
      formTitle: '',
      isEdit: false,
      editId: null,
      formData: {
        date: moment(),
        round: null
      },
      roundOptions: [
        { value: 1, label: '春夏灌' },
        { value: 2, label: '秋灌' },
        { value: 3, label: '秋浇' }
      ],
      tableData: [],
      // 永济干渠表格数据
      canalTableData: [],
      // 防抖计时器
      debounceTimers: new Map(),
      // 计算请求队列
      calculationQueue: new Map(),
      // 列表第一条记录的日期，用于限制新增时的日期选择范围
      firstRecordDate: null
    }
  },
  methods: {
    // 禁用日期的逻辑
    disabledDate(current) {
      if (!current) return false

      // 禁用今天之后的日期
      if (current > moment().endOf('day')) {
        return true
      }

      // 如果是新增模式且有第一条记录的日期，则限制可选择的日期范围
      if (!this.isEdit && this.firstRecordDate) {
        const firstDate = moment(this.firstRecordDate)
        // 只能选择第一条记录日期的下一天到今天
        return current <= firstDate.endOf('day')
      }

      return false
    },

    cancel() {
      this.open = false
      this.loading = false
      this.resetData()
    },

    // 重置数据
    resetData() {
      this.isEdit = false
      this.editId = null
      this.firstRecordDate = null
      this.formData = {
        date: moment(),
        round: null
      }
      this.tableData = []
      this.canalTableData = []
      // 清理防抖计时器
      this.debounceTimers.clear()
      this.calculationQueue.clear()
    },

    handleAdd(firstRecordDate = null) {
      this.open = true
      this.formTitle = '新增'
      this.isEdit = false
      this.editId = null
      this.firstRecordDate = firstRecordDate
      this.initData(firstRecordDate)
    },

    async handleUpdate(record) {
      this.open = true
      this.formTitle = '编辑'
      this.isEdit = true
      this.editId = record.id
      this.modalLoading = true

      try {
        await this.loadEditData(record.id)
      } catch (error) {
        console.error('加载编辑数据失败:', error)
        this.$message.error('加载编辑数据失败')
      } finally {
        this.modalLoading = false
      }
    },

    // 加载编辑数据
    async loadEditData(id) {
      try {
        // 获取详情数据
        const response = await getIrrigationWaterSituationById(id)

        if (response.success && response.data) {
          const detailData = response.data

          // 设置基本信息
          this.formData = {
            date: detailData.fillDate ? moment(detailData.fillDate) : moment(),
            round: detailData.roundType
          }

          // 初始化表格结构
          this.initTableData()
          await this.initCanalTableData()

          // 填充数据
          this.fillTableData(detailData)
          this.fillCanalTableData(detailData)
        } else {
          this.$message.error(response.message || '获取详情数据失败')
        }
      } catch (error) {
        console.error('加载编辑数据失败:', error)
        this.$message.error('加载编辑数据失败')
      }
    },

    // 填充水情表数据
    fillTableData(detailData) {
      if (!detailData.waterSituationDetailVOList) return

      this.tableData.forEach(row => {
        if (row.projectId) {
          const detail = detailData.waterSituationDetailVOList.find(
            item => item.channelNo === (row.projectCode || row.projectId.toString())
          )

          if (detail) {
            row.flow8 = detail.eightFlowRate || null
            row.flow18 = detail.eighteenFlowRate || null
            row.dailyFlow = detail.dailyFlowRate != null ? detail.dailyFlowRate.toFixed(2) : '0.00'
            row.cumulativeFlow = detail.dailyFlowRateTotal != null ? detail.dailyFlowRateTotal.toFixed(2) : '0.00'
            row.remark = detail.remark || ''
            row.openTime = detail.openTime ? moment(detail.openTime, 'YYYY-MM-DD HH:mm:ss') : null
            row.closeTime = detail.closeTime ? moment(detail.closeTime, 'YYYY-MM-DD HH:mm:ss') : null
          }
        }
      })

      // 重新计算合计行
      this.calculateAllTotals()
    },

    // 填充永济干渠表数据
    fillCanalTableData(detailData) {
      if (!detailData.waterStageDetailDtoList) return

      this.canalTableData.forEach(row => {
        if (row.projectId) {
          const detail = detailData.waterStageDetailDtoList.find(
            item => item.gateNo === (row.projectCode || row.projectId.toString()) && item.gateDirection === row.direction
          )

          if (detail) {
            row.waterLevel8 = detail.eightWaterStage || null
            row.canalFlow8 = detail.eightFlowRate || null
            row.waterLevel18 = detail.eighteenWaterStage || null
            row.canalFlow18 = detail.eighteenFlowRate || null
          }
        }
      })
    },

    // 初始化数据
    async initData(firstRecordDate = null) {
      // 如果传入了列表第一条数据的日期，则设置为该日期的下一天
      if (firstRecordDate) {
        this.formData.date = moment(firstRecordDate).add(1, 'day')
      } else {
        this.formData.date = moment()
      }
      this.formData.round = null // 先清空轮次
      await this.initRound()
      this.initTableData()
      await this.initCanalTableData()
    },

    // 根据日期初始化轮次
    async initRound() {
      try {
        const dateStr = this.formData.date.format('YYYY-MM-DD')
        const response = await getIrrigationRound(dateStr, dateStr)

        if (response.success && response.data) {
          // 检查灌溉轮次是否为null
          if (response.data.irrigationRound === null) {
            this.formData.round = null
            this.$message.warning('该日期不在灌溉轮次中，请重新选择日期')
            return
          }
          this.formData.round = response.data.irrigationRound
        } else {
          this.$message.error(response.message || '获取灌溉轮次失败')
        }
      } catch (error) {
        console.error('获取灌溉轮次失败:', error)
        this.$message.error('获取灌溉轮次失败')
      }
    },

    // 日期变化事件
    async onDateChange(date) {
      this.formData.date = date
      // 只有新增模式才自动获取轮次
      if (!this.isEdit) {
        // 先清空轮次，避免显示旧的轮次信息
        this.formData.round = null
        await this.initRound()
      }
    },

    // 获取轮次标签
    getRoundLabel(roundValue) {
      const option = this.roundOptions.find(item => item.value === roundValue)
      return option ? option.label : ''
    },

    // 格式化日期显示
    formatDateDisplay(date) {
      if (!date) return ''
      if (moment.isMoment(date)) {
        return date.format('YYYY-MM-DD')
      }
      return moment(date).format('YYYY-MM-DD')
    },

    // 流量变化事件（新增）
    onFlowChange(row) {
      this.debouncedCalculateFlow(row)
    },

    // 时间变化事件
    onTimeChange(row, field, time) {
      row[field] = time
      this.debouncedCalculateFlow(row)
    },

    // 防抖计算流量
    debouncedCalculateFlow(row) {
      if (row.isTotal || !row.projectId) return

      const rowId = row.id

      // 清除之前的计时器
      if (this.debounceTimers.has(rowId)) {
        clearTimeout(this.debounceTimers.get(rowId))
      }

      // 设置新的计时器
      const timer = setTimeout(() => {
        this.calculateRowFlow(row)
        this.debounceTimers.delete(rowId)
      }, 800) // 800ms防抖延迟

      this.debounceTimers.set(rowId, timer)
    },

    // 计算单行流量
    async calculateRowFlow(row) {
      if (row.isTotal || !row.projectId) return

      try {
        // 构建请求参数
        const requestData = {
          fillDate: this.formData.date.format('YYYY-MM-DD'),
          cstIrrigationWaterSituationDetailDto: {
            channelNo: row.projectCode || row.projectId.toString(),
            closeTime: this.formatDateTime(row.closeTime),
            eightFlowRate: parseFloat(row.flow8) || 0,
            eighteenFlowRate: parseFloat(row.flow18) || 0,
            irrigationNo: this.getIrrigationNo(row),
            openTime: this.formatDateTime(row.openTime),
            parentChannelNo: this.getParentChannelNo(row),
            remark: row.remark || ''
          }
        }

        // 调用计算接口
        const response = await calculateIrrigationWaterSituation(requestData)

        if (response.success && response.data) {
          // 更新计算结果，null值显示为0
          row.dailyFlow = response.data.dailyFlowRate != null ? response.data.dailyFlowRate.toFixed(2) : '0.00'
          row.cumulativeFlow = response.data.dailyFlowRateTotal != null ? response.data.dailyFlowRateTotal.toFixed(2) : '0.00'

          // 重新计算合计行
          this.calculateAllTotals()
        } else {
          // 接口调用失败或无数据时设置默认值
          row.dailyFlow = '0.00'
          row.cumulativeFlow = '0.00'
        }
      } catch (error) {
        console.error('计算流量失败:', error)
        // 接口异常时设置默认值
        row.dailyFlow = '0.00'
        row.cumulativeFlow = '0.00'
      }
    },

    // 格式化时间显示
    formatTime(time) {
      if (!time) return '-'
      if (moment.isMoment(time)) {
        return time.format('HH:mm')
      }
      return '-'
    },

    // 初始化表格数据
    initTableData() {
      try {
        this.tableData = []
        let currentId = 1

        // 写死的配置数据
        const staticConfig = [
          // 总干渠首
          {
            projectCode: 'GQ001',
            projectId: 4828,
            irrigationArea: '总干渠首',
            channelGroup: '总干渠首',
            channel: '',
            level: 0,
            hasSubChannels: false,
            isTotal: false
          },
          // 永济灌域
          {
            projectCode: 'GQ002',
            projectId: 4829,
            irrigationArea: '永济灌域',
            channelGroup: '永济渠',
            channel: '',
            level: 0,
            hasSubChannels: false,
            isTotal: false
          },
          // 合济渠
          {
            projectCode: 'GQ005',
            projectId: 4838,
            irrigationArea: '',
            channelGroup: '合济渠',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: false
          },
          // 南边渠
          {
            projectCode: 'GQ012',
            projectId: 4888,
            irrigationArea: '',
            channelGroup: '南边渠',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: false
          },
          // 北边渠
          {
            projectCode: 'GQ004',
            projectId: 4837,
            irrigationArea: '',
            channelGroup: '北边渠',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: false
          },
          // 永济灌域合计
          {
            projectCode: null,
            projectId: null,
            irrigationArea: '',
            channelGroup: '灌域合计',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: true
          },
          // 永济渠灌域
          {
            projectCode: 'GQ006',
            projectId: 4839,
            irrigationArea: '永济渠灌域',
            channelGroup: '永兰渠',
            channel: '',
            level: 0,
            hasSubChannels: false,
            isTotal: false
          },
          // 永刚渠
          {
            projectCode: 'GQ007',
            projectId: 4840,
            irrigationArea: '',
            channelGroup: '永刚渠',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: false
          },
          // 西乐渠
          {
            projectCode: 'GQ008',
            projectId: 4841,
            irrigationArea: '',
            channelGroup: '西乐渠',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: false
          },
          // 新华渠
          {
            projectCode: 'GQ009',
            projectId: 4842,
            irrigationArea: '',
            channelGroup: '新华渠',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: false
          },
          // 正稍渠
          {
            projectCode: 'GQ010',
            projectId: 4886,
            irrigationArea: '',
            channelGroup: '正稍渠',
            channel: '正稍',
            level: 2,
            hasSubChannels: true,
            isTotal: false,
            parentProjectCode: 'GQ002'
          },
          // 大退水
          {
            projectCode: 'GQ011',
            projectId: 4887,
            irrigationArea: '',
            channelGroup: '',
            channel: '大退水',
            level: 2,
            hasSubChannels: true,
            isTotal: false,
            parentProjectCode: 'GQ002'
          },
          // 正稍渠合计
          {
            projectCode: null,
            projectId: null,
            irrigationArea: '',
            channelGroup: '',
            channel: '正稍渠合计',
            level: 2,
            hasSubChannels: false,
            isTotal: true
          },
          // 干渠直口
          {
            projectCode: 'GQ181',
            projectId: 5056,
            irrigationArea: '',
            channelGroup: '干渠直口',
            channel: '二号渠',
            level: 2,
            hasSubChannels: true,
            isTotal: false,
            parentProjectCode: 'GQ002'
          },
          // 甜菜渠
          {
            projectCode: 'GQ202',
            projectId: 5077,
            irrigationArea: '',
            channelGroup: '',
            channel: '甜菜渠',
            level: 2,
            hasSubChannels: true,
            isTotal: false,
            parentProjectCode: 'GQ002'
          },
          // 新济渠
          {
            projectCode: 'GQ187',
            projectId: 5062,
            irrigationArea: '',
            channelGroup: '',
            channel: '新济渠',
            level: 2,
            hasSubChannels: true,
            isTotal: false,
            parentProjectCode: 'GQ002'
          },
          // 干渠直口合计
          {
            projectCode: null,
            projectId: null,
            irrigationArea: '',
            channelGroup: '',
            channel: '干渠直口合计',
            level: 2,
            hasSubChannels: false,
            isTotal: true
          },
          // 永济渠合计
          {
            projectCode: null,
            projectId: null,
            irrigationArea: '',
            channelGroup: '永济渠合计',
            channel: '',
            level: 1,
            hasSubChannels: false,
            isTotal: true
          }
        ]

        // 根据配置生成表格数据
        staticConfig.forEach(config => {
          this.tableData.push({
            id: currentId++,
            projectId: config.projectId,
            projectCode: config.projectCode,
            irrigationArea: config.irrigationArea,
            channelGroup: config.channelGroup,
            channel: config.channel,
            flow8: null,
            flow18: null,
            dailyFlow: null,
            cumulativeFlow: null,
            remark: '',
            openTime: null,
            closeTime: null,
            isTotal: config.isTotal,
            level: config.level,
            hasSubChannels: config.hasSubChannels,
            parentProjectCode: config.parentProjectCode || null
          })
        })

      } catch (error) {
        console.error('初始化表格数据失败:', error)
        this.$message.error('初始化表格数据失败')
      }
    },


    // 单元格合并方法
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      // 灌域列合并
      if (column.property === 'irrigationArea') {
        const cellValue = row[column.property]

        // 特殊处理：总干渠首行需要合并灌域和渠道两列（三格）
        if (cellValue === '总干渠首') {
          return { rowspan: 1, colspan: 3 }
        }

        // 永济灌域和永济渠灌域需要向下合并
        if (cellValue === '永济灌域' || cellValue === '永济渠灌域') {
          // 计算需要合并的行数
          let countRowspan = 1
          let nextRow = visibleData[_rowIndex + 1]

          if (cellValue === '永济灌域') {
            // 永济灌域向下合并到永济渠灌域之前（不包括永济渠灌域），再多合并一行
            while (nextRow && nextRow.irrigationArea === '' && nextRow.irrigationArea !== '永济渠灌域') {
              if (nextRow.channelGroup === '灌域合计') {
                // 包含灌域合计行
                countRowspan++
                break
              }
              nextRow = visibleData[++countRowspan + _rowIndex]
            }
          } else if (cellValue === '永济渠灌域') {
            // 永济渠灌域向下合并到永济渠合计之前，再多合并一行
            while (nextRow && nextRow.irrigationArea === '') {
              if (nextRow.channelGroup === '永济渠合计') {
                // 包含永济渠合计行
                countRowspan++
                break
              }
              nextRow = visibleData[++countRowspan + _rowIndex]
            }
          }

          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }

        // 处理被合并行的灌域列隐藏
        if (!cellValue) {
          // 查找当前行是否属于某个灌域的合并范围
          for (let i = _rowIndex - 1; i >= 0; i--) {
            const prevRow = visibleData[i]
            if (prevRow.irrigationArea === '永济灌域' || prevRow.irrigationArea === '永济渠灌域') {
              // 检查当前行是否在合并范围内
              let shouldHide = false
              if (prevRow.irrigationArea === '永济灌域') {
                shouldHide = row.irrigationArea !== '永济渠灌域'
              } else if (prevRow.irrigationArea === '永济渠灌域') {
                shouldHide = true // 永济渠灌域下的所有行都被合并
              }

              if (shouldHide) {
                return { rowspan: 0, colspan: 0 }
              }
              break
            }
            if (prevRow.irrigationArea) {
              break // 遇到其他灌域就停止查找
            }
          }
        }

        // 其他情况的灌域列合并（原有逻辑）
        if (cellValue) {
          const prevRow = visibleData[_rowIndex - 1]
          let nextRow = visibleData[_rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let countRowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              nextRow = visibleData[++countRowspan + _rowIndex]
            }
            if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 }
            }
          }
        }
      }

      // 渠道组列合并 - 针对有子渠道的情况
      if (column.property === 'channelGroup') {
        const cellValue = row[column.property]

        // 总干渠首行的渠道组列需要隐藏（已被灌域列合并）
        if (row.irrigationArea === '总干渠首') {
          return { rowspan: 0, colspan: 0 }
        }

        // 处理正稍渠和干渠直口的向下合并
        if (cellValue === '正稍渠' || cellValue === '干渠直口') {
          // 计算需要合并的行数，包括所有子渠道和合计行
          let countRowspan = 1
          let nextRow = visibleData[_rowIndex + 1]

          while (nextRow) {
            // 如果是同组的子渠道（channel不为空且hasSubChannels为true）
            if (nextRow.hasSubChannels && nextRow.channel && nextRow.channelGroup === '') {
              countRowspan++
              nextRow = visibleData[_rowIndex + countRowspan]
            }
            // 如果是该组的合计行
            else if (nextRow.channel === `${cellValue}合计` && nextRow.isTotal) {
              countRowspan++
              break
            }
            else {
              break
            }
          }

          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }

        // 处理正稍渠和干渠直口子渠道的渠道组列隐藏
        if (!cellValue && row.hasSubChannels && row.channel) {
          // 检查是否属于正稍渠或干渠直口的子渠道
          for (let i = _rowIndex - 1; i >= 0; i--) {
            const prevRow = visibleData[i]
            if (prevRow.channelGroup === '正稍渠' || prevRow.channelGroup === '干渠直口') {
              return { rowspan: 0, colspan: 0 }
            }
            if (prevRow.channelGroup && prevRow.channelGroup !== '') {
              break
            }
          }
        }

        // 处理正稍渠合计和干渠直口合计行的渠道组列隐藏
        if (!cellValue && row.isTotal && (row.channel === '正稍渠合计' || row.channel === '干渠直口合计')) {
          return { rowspan: 0, colspan: 0 }
        }

        // 处理没有子渠道的行，渠道组列需要跨列显示（但排除总干渠首）
        if (cellValue && !row.hasSubChannels && row.channel === '' && row.irrigationArea !== '总干渠首') {
          return { rowspan: 1, colspan: 2 }
        }
      }

      // 渠道列处理
      if (column.property === 'channel') {
        // 总干渠首行的渠道列需要隐藏（已被灌域列合并）
        if (row.irrigationArea === '总干渠首') {
          return { rowspan: 0, colspan: 0 }
        }



        // 对于没有子渠道且渠道为空的行，隐藏渠道列
        if (!row.hasSubChannels && !row.channel) {
          return { rowspan: 0, colspan: 0 }
        }
      }

      return null
    },

    // 永济干渠表格单元格合并方法
    mergeCanalRowMethod({ row, _rowIndex, column, visibleData }) {
      // 永济干渠列合并 - 整列合并
      if (column.property === 'canalName') {
        if (_rowIndex === 0) {
          return { rowspan: visibleData.length, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }

      // 永济节制闸列合并 - 每个闸合并两行
      if (column.property === 'gateName') {
        const cellValue = row[column.property]
        if (cellValue) {
          // 检查是否是闸名称行（包含"闸"的项目名称）
          if (cellValue.includes('闸')) {
            return { rowspan: 2, colspan: 1 }
          }
        } else {
          // 空值的行需要隐藏（合并到上一行）
          return { rowspan: 0, colspan: 0 }
        }
      }

      // gatePosition列不需要合并，正常显示
      return null
    },

    // 初始化永济干渠表格数据
    async initCanalTableData() {
      try {
        this.canalTableData = []
        let currentId = 1

        // 获取永济干渠的子节点（闸）
        const topLevelResponse = await getProjectChildren({ parentId: 0 })
        if (!topLevelResponse.success) {
          console.error('获取顶级节点失败')
          return
        }

        const topLevelData = topLevelResponse.data || []
        const yongjiIrrigation = topLevelData.find(item => item.projectName === '永济灌区')

        if (yongjiIrrigation) {
          // 获取永济灌区的子节点
          const yongjiChildrenResponse = await getProjectChildren({ parentId: yongjiIrrigation.projectId })
          if (yongjiChildrenResponse.success) {
            const yongjiChildren = yongjiChildrenResponse.data || []
            const yongjiCanal = yongjiChildren.find(item => item.projectName === '永济渠')

            if (yongjiCanal) {
              // 获取永济干渠的子节点（包含闸）
              const canalChildrenResponse = await getProjectChildren({ parentId: yongjiCanal.projectId })
              if (canalChildrenResponse.success) {
                const canalChildren = canalChildrenResponse.data || []

                // 按sort排序
                canalChildren.sort((a, b) => a.sort - b.sort)

                // 过滤出包含"闸"的项目
                // const gates = canalChildren.filter(child => child.projectName.includes('闸'))
                const gates = [
                  {
                    projectId: 5664,
                    projectCode: 'SZ001',
                    projectName: '一闸'
                  },
                  {
                    projectId: 5665,
                    projectCode: 'SZ002',
                    projectName: '二闸'
                  },
                  {
                    projectId: 5666,
                    projectCode: 'SZ003',
                    projectName: '三闸'
                  },
                ]
                // 动态生成闸的数据
                gates.forEach((gate, index) => {
                  // 闸上
                  this.canalTableData.push({
                    id: currentId++,
                    projectId: gate.projectId,
                    projectCode: gate.projectCode,
                    canalName: index === 0 ? '永济干渠' : '', // 只有第一个闸显示永济干渠
                    gateName: gate.projectName,
                    gatePosition: '闸上',
                    waterLevel8: null,
                    waterLevel18: null,
                    canalFlow8: null,
                    canalFlow18: null,
                    direction: 1, // 添加direction字段，闸上为1
                    isTotal: false
                  })

                  // 闸下
                  this.canalTableData.push({
                    id: currentId++,
                    projectId: gate.projectId,
                    projectCode: gate.projectCode,
                    canalName: '',
                    gateName: '',
                    gatePosition: '闸下',
                    waterLevel8: null,
                    waterLevel18: null,
                    canalFlow8: null,
                    canalFlow18: null,
                    direction: 2, // 添加direction字段，闸下为2
                    isTotal: false
                  })
                })
              }
            }
          }
        }

        // 如果没有获取到数据，使用默认数据
        if (this.canalTableData.length === 0) {
          this.setDefaultCanalTableData()
        }

      } catch (error) {
        console.error('初始化永济干渠表格数据失败:', error)
        // 出错时使用默认数据
        this.setDefaultCanalTableData()
      }
    },

    // 设置默认的永济干渠表格数据
    setDefaultCanalTableData() {
      this.canalTableData = [
        // 一闸
        {
          id: 1,
          projectId: 5664,
          projectCode: 'SZ001',
          canalName: '永济干渠',
          gateName: '永济一闸',
          gatePosition: '闸上',
          waterLevel8: null,
          waterLevel18: null,
          canalFlow8: null,
          canalFlow18: null,
          direction: 1, // 添加direction字段，闸上为1
          isTotal: false
        },
        {
          id: 2,
          projectId: 5664,
          projectCode: 'SZ001',
          canalName: '',
          gateName: '',
          gatePosition: '闸下',
          waterLevel8: null,
          waterLevel18: null,
          canalFlow8: null,
          canalFlow18: null,
          direction: 2, // 添加direction字段，闸下为2
          isTotal: false
        },
        // 二闸
        {
          id: 3,
          projectId: 5665,
          projectCode: 'SZ002',
          canalName: '',
          gateName: '永济二闸',
          gatePosition: '闸上',
          waterLevel8: null,
          waterLevel18: null,
          canalFlow8: null,
          canalFlow18: null,
          direction: 1, // 添加direction字段，闸上为1
          isTotal: false
        },
        {
          id: 4,
          projectId: 5665,
          projectCode: 'SZ002',
          canalName: '',
          gateName: '',
          gatePosition: '闸下',
          waterLevel8: null,
          waterLevel18: null,
          canalFlow8: null,
          canalFlow18: null,
          direction: 2, // 添加direction字段，闸下为2
          isTotal: false
        },
        // 三闸
        {
          id: 5,
          projectId: 5666,
          projectCode: 'SZ003',
          canalName: '',
          gateName: '永济三闸',
          gatePosition: '闸上',
          waterLevel8: null,
          waterLevel18: null,
          canalFlow8: null,
          canalFlow18: null,
          direction: 1, // 添加direction字段，闸上为1
          isTotal: false
        },
        {
          id: 6,
          projectId: 5666,
          projectCode: 'SZ003',
          canalName: '',
          gateName: '',
          gatePosition: '闸下',
          waterLevel8: null,
          waterLevel18: null,
          canalFlow8: null,
          canalFlow18: null,
          direction: 2, // 添加direction字段，闸下为2
          isTotal: false
        }
      ]
    },

    // 计算合计行
    calculateTotalFlow(totalRow) {
      // 根据合计行的类型找到需要统计的行
      let rowsToSum = []
      const totalIndex = this.tableData.findIndex(row => row.id === totalRow.id)

      if (totalRow.channelGroup === '灌域合计') {
        // 永济灌域合计：统计永济灌域下的所有非合计行
        rowsToSum = this.getRelatedRows(totalIndex, '永济灌域')
      } else if (totalRow.channelGroup === '永济渠合计') {
        // 永济渠合计：统计永济渠灌域下的所有非合计行
        rowsToSum = this.getRelatedRows(totalIndex, '永济渠灌域')
      } else if (totalRow.channel === '正稍渠合计') {
        // 正稍渠合计：统计正稍渠组下的所有非合计行
        rowsToSum = this.getSubChannelRows(totalIndex, '正稍渠')
      } else if (totalRow.channel === '干渠直口合计') {
        // 干渠直口合计：统计干渠直口组下的所有非合计行
        rowsToSum = this.getSubChannelRows(totalIndex, '干渠直口')
      }

      // 计算合计
      let totalFlow8 = 0
      let totalFlow18 = 0
      let totalDailyFlow = 0
      let totalCumulativeFlow = 0

      rowsToSum.forEach(row => {
        totalFlow8 += parseFloat(row.flow8) || 0
        totalFlow18 += parseFloat(row.flow18) || 0
        totalDailyFlow += parseFloat(row.dailyFlow) || 0
        totalCumulativeFlow += parseFloat(row.cumulativeFlow) || 0
      })

      totalRow.flow8 = totalFlow8 > 0 ? totalFlow8.toFixed(2) : null
      totalRow.flow18 = totalFlow18 > 0 ? totalFlow18.toFixed(2) : null
      totalRow.dailyFlow = totalDailyFlow > 0 ? totalDailyFlow.toFixed(2) : null
      totalRow.cumulativeFlow = totalCumulativeFlow > 0 ? totalCumulativeFlow.toFixed(2) : null
    },

    // 获取相关行（用于灌域合计）
    getRelatedRows(totalIndex, irrigationAreaName) {
      const rows = []

      // 向上查找，找到对应的灌域开始行
      let startIndex = -1
      for (let i = totalIndex - 1; i >= 0; i--) {
        const row = this.tableData[i]
        if (row.irrigationArea === irrigationAreaName) {
          startIndex = i
          break
        }
      }

      if (startIndex >= 0) {
        // 从灌域开始行到合计行之间的所有非合计行
        for (let i = startIndex; i < totalIndex; i++) {
          const row = this.tableData[i]
          if (!row.isTotal && row.projectId) {
            rows.push(row)
          }
        }
      }

      return rows
    },

    // 获取子渠道行（用于正稍渠和干渠直口合计）
    getSubChannelRows(totalIndex, channelGroupName) {
      const rows = []

      // 向上查找，找到对应的渠道组开始行
      let startIndex = -1
      for (let i = totalIndex - 1; i >= 0; i--) {
        const row = this.tableData[i]
        if (row.channelGroup === channelGroupName && row.hasSubChannels) {
          startIndex = i
          break
        }
      }

      if (startIndex >= 0) {
        // 从渠道组开始行到合计行之间的所有非合计行
        for (let i = startIndex; i < totalIndex; i++) {
          const row = this.tableData[i]
          if (!row.isTotal && row.projectId && row.level === 2) {
            rows.push(row)
          }
        }
      }

      return rows
    },

    // 计算所有合计行
    calculateAllTotals() {
      const totalRows = this.tableData.filter(row => row.isTotal)
      totalRows.forEach(row => {
        this.calculateTotalFlow(row)
      })
    },

    // 保存
    async handleSave() {
      try {
        this.loading = true

        // 验证必填数据
        if (!this.formData.date) {
          this.$message.error('请选择日期')
          return
        }

        if (!this.formData.round) {
          this.$message.error('该日期不在灌溉轮次中，请重新选择日期')
          return
        }

        // 构建保存数据
        const saveData = this.buildSaveData()

        console.log('保存数据:', saveData)

        let response
        if (this.isEdit) {
          // 编辑模式：添加id参数
          saveData.id = this.editId
          response = await updateIrrigationWaterSituation(saveData)
        } else {
          // 新增模式
          response = await addWaterSituationReport(saveData)
        }

        if (response.success) {
          this.$message.success(this.isEdit ? '修改成功' : '保存成功')
          this.open = false
          this.$emit('ok')
        } else {
          this.$message.error(response.message || (this.isEdit ? '修改失败' : '保存失败'))
        }
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error(this.isEdit ? '修改失败' : '保存失败')
      } finally {
        this.loading = false
      }
    },

    // 构建保存数据
    buildSaveData() {
      const fillDate = this.formData.date.format('YYYY-MM-DD')
      const roundType = this.formData.round

      // 构建水情详情数据
      const cstIrrigationWaterSituationDetailDtoList = this.buildWaterSituationDetails()

      // 构建水位详情数据
      const waterStageDetailDtoList = this.buildWaterStageDetails()

      return {
        fillDate,
        roundType,
        cstIrrigationWaterSituationDetailDtoList,
        waterStageDetailDtoList
      }
    },

    // 构建水情详情数据
    buildWaterSituationDetails() {
      const details = []

      // 过滤出有效的水情数据行（排除合计行）
      const validRows = this.tableData.filter(row =>
        !row.isTotal &&
        row.projectId &&
        (row.flow8 || row.flow18 || row.openTime || row.closeTime || row.remark)
      )

      validRows.forEach(row => {
        const detail = {
          channelNo: row.projectCode || row.projectId.toString(),
          closeTime: this.formatDateTime(row.closeTime),
          eightFlowRate: parseFloat(row.flow8) || 0,
          eighteenFlowRate: parseFloat(row.flow18) || 0,
          irrigationNo: this.getIrrigationNo(row),
          openTime: this.formatDateTime(row.openTime),
          parentChannelNo: this.getParentChannelNo(row),
          remark: row.remark || ''
        }
        details.push(detail)
      })

      return details
    },

    // 构建水位详情数据
    buildWaterStageDetails() {
      const details = []

      // 过滤出有效的水位数据行
      const validRows = this.canalTableData.filter(row =>
        row.projectId &&
        (row.waterLevel8 || row.waterLevel18 || row.canalFlow8 || row.canalFlow18)
      )
      console.log('validRows', validRows, this.canalTableData)
      validRows.forEach(row => {
        const detail = {
          eightFlowRate: parseFloat(row.canalFlow8) || 0,
          eightWaterStage: parseFloat(row.waterLevel8) || 0,
          eighteenFlowRate: parseFloat(row.canalFlow18) || 0,
          eighteenWaterStage: parseFloat(row.waterLevel18) || 0,
          gateDirection: row.direction,
          gateNo: row.projectCode || row.projectId.toString()
        }
        details.push(detail)
      })

      return details
    },

    // 格式化日期时间
    formatDateTime(time) {
      if (!time) return ''

      const date = this.formData.date
      const timeStr = time.format('HH:mm')
      return `${date.format('YYYY-MM-DD')} ${timeStr}:00`
    },

    // 获取灌区编码
    getIrrigationNo(row) {
      console.log('row:',row)
      // 根据行的层级和灌域信息确定灌区编码
      if (row.irrigationArea === '总干渠首') {
        return '0'
      }

      if (row.irrigationArea === '永济灌域') {
        return 'P001'
      }

      // 查找所属的灌域
      const irrigationAreaRow = this.findIrrigationArea(row)
      console.log('irrigationAreaRow',irrigationAreaRow)
      if (irrigationAreaRow) {
        // 如果是永济灌域，返回固定编码P001
        if (irrigationAreaRow.irrigationArea === '永济灌域' ||
          irrigationAreaRow.irrigationArea === '永济渠灌域') {
          return 'P001'
        }

        if (irrigationAreaRow.projectCode) {
          return irrigationAreaRow.projectCode
        }
      }

      return '0'
    },

    // 获取上级渠道编码
    getParentChannelNo(row) {
      // 根据行的层级确定上级渠道编码
      if (row.level === 0 || row.level === 1) {
        return '0'
      }

      // 对于level为2的行，使用存储的parentProjectCode
      if (row.level === 2 && row.parentProjectCode) {
        return row.parentProjectCode
      }

      return '0'
    },

    // 查找所属灌域
    findIrrigationArea(targetRow) {
      // 向上查找最近的有灌域名称的行
      const targetIndex = this.tableData.findIndex(row => row.id === targetRow.id)

      for (let i = targetIndex - 1; i >= 0; i--) {
        const row = this.tableData[i]
        if (row.irrigationArea && row.level === 0) {
          return row
        }
      }

      return null
    }
  }
}

</script>

<style lang="less" scoped>
.irrigation-rounds-crop-reporting {
  // padding: 20px;
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .form-row {
      display: flex;
      gap: 30px;

      .form-item {
        display: flex;
        align-items: center;

        label {
          margin-right: 8px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  .water-table-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .water-table {
      width: 100%;

      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }

      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
      }

      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }

      /deep/ .vxe-table {
        width: 100% !important;
      }
    }
  }

  .canal-table-section {
    // margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .canal-table {
      width: 100%;

      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }

      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
      }

      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }

      /deep/ .vxe-table {
        width: 100% !important;
      }
    }
  }

  .action-buttons {
    text-align: center;

    .ant-btn {
      margin: 0 10px;
      min-width: 80px;
    }
  }

  .round-display,
  .date-display {
    display: inline-block;
    width: 200px;
    padding: 4px 11px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #f5f5f5;
    color: #000000d9;
    font-size: 14px;
    line-height: 1.5715;
    min-height: 32px;
    box-sizing: border-box;
  }
}

/deep/ .vxe-header--column,
/deep/ .vxe-body--column {
  text-align: center !important;
  justify-content: center !important;
  align-items: center !important;
}
</style>